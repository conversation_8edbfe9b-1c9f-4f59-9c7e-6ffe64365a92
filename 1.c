#include <stdio.h>
#include <stdlib.h>

int main() {
    int n;
    scanf("%d", &n);
    
    int h[105][105];
    for (int i = 1; i <= n; i++) {
        for (int j = 1; j <= n; j++) {
            scanf("%d", &h[i][j]);
        }
    }
    
    // 对每一行计算 l_i, r_i, u_i, d_i
    for (int i = 1; i <= n; i++) {
        int l_i = 0, r_i = 0, u_i = 0, d_i = 0;
        
        // 计算 l_i: 从第 i 行的左侧看去，能看到多少根柱子
        int max_height = 0;
        for (int j = 1; j <= n; j++) {
            if (h[i][j] > max_height) {
                l_i++;
                max_height = h[i][j];
            }
        }
        
        // 计算 r_i: 从第 i 行的右侧看去，能看到多少根柱子
        max_height = 0;
        for (int j = n; j >= 1; j--) {
            if (h[i][j] > max_height) {
                r_i++;
                max_height = h[i][j];
            }
        }
        
        // 计算 u_i: 从第 i 列的上侧看去，能看到多少根柱子
        max_height = 0;
        for (int j = 1; j <= n; j++) {
            if (h[j][i] > max_height) {
                u_i++;
                max_height = h[j][i];
            }
        }
        
        // 计算 d_i: 从第 i 列的下侧看去，能看到多少根柱子
        max_height = 0;
        for (int j = n; j >= 1; j--) {
            if (h[j][i] > max_height) {
                d_i++;
                max_height = h[j][i];
            }
        }
        
        printf("%d %d %d %d\n", l_i, r_i, u_i, d_i);
    }
    
    return 0;
}
