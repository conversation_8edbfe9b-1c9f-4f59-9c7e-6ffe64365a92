/Applications/CLion.app/Contents/bin/cmake/mac/aarch64/bin/cmake -DCMAKE_BUILD_TYPE=Debug -DC<PERSON><PERSON>_MAKE_PROGRAM=/Applications/CLion.app/Contents/bin/ninja/mac/aarch64/ninja -G Ninja -S /Users/<USER>/Desktop/test/23-25/anser -B /Users/<USER>/Desktop/test/23-25/anser/cmake-build-debug
-- The C compiler identification is unknown
-- The CXX compiler identification is unknown
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - failed
-- Check for working C compiler: /usr/bin/cc
-- Check for working C compiler: /usr/bin/cc - broken
CMake Error at /Applications/CLion.app/Contents/bin/cmake/mac/aarch64/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:67 (message):
  The C compiler

    "/usr/bin/cc"

  is not able to compile a simple test program.

  It fails with the following output:

    Change Dir: '/Users/<USER>/Desktop/test/23-25/anser/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-sk7EF6'
    
    Run Build Command(s): /Applications/CLion.app/Contents/bin/ninja/mac/aarch64/ninja -v cmTC_cf65c
    [1/2] /usr/bin/cc   -arch arm64 -o CMakeFiles/cmTC_cf65c.dir/testCCompiler.c.o -c /Users/<USER>/Desktop/test/23-25/anser/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-sk7EF6/testCCompiler.c
    FAILED: CMakeFiles/cmTC_cf65c.dir/testCCompiler.c.o 
    /usr/bin/cc   -arch arm64 -o CMakeFiles/cmTC_cf65c.dir/testCCompiler.c.o -c /Users/<USER>/Desktop/test/23-25/anser/cmake-build-debug/CMakeFiles/CMakeScratch/TryCompile-sk7EF6/testCCompiler.c
    xcrun: error: invalid active developer path (/Library/Developer/CommandLineTools), missing xcrun at: /Library/Developer/CommandLineTools/usr/bin/xcrun
    ninja: build stopped: subcommand failed.
    
    

  

  CMake will not be able to correctly generate this project.
Call Stack (most recent call first):
  CMakeLists.txt:2 (project)


-- Configuring incomplete, errors occurred!
