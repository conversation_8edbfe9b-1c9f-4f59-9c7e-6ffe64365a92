#include <stdio.h>

long long min_value(long long x, long long n, long long m) {
    // 为了得到最小值，我们应该先进行操作2（向上取整），再进行操作1（向下取整）
    long long result = x;
    
    // 先进行m次操作2
    for (int i = 0; i < m; i++) {
        result = (result + 1) / 2;
    }
    
    // 再进行n次操作1
    for (int i = 0; i < n; i++) {
        result = result / 2;
    }
    
    return result;
}

long long max_value(long long x, long long n, long long m) {
    // 为了得到最大值，我们应该先进行操作1（向下取整），再进行操作2（向上取整）
    long long result = x;
    
    // 先进行n次操作1
    for (int i = 0; i < n; i++) {
        result = result / 2;
    }
    
    // 再进行m次操作2
    for (int i = 0; i < m; i++) {
        result = (result + 1) / 2;
    }
    
    return result;
}

int main() {
    int T;
    scanf("%d", &T);
    
    while (T--) {
        long long x, n, m;
        scanf("%lld %lld %lld", &x, &n, &m);
        
        long long min_val = min_value(x, n, m);
        long long max_val = max_value(x, n, m);
        
        printf("%lld %lld\n", min_val, max_val);
    }
    
    return 0;
}
