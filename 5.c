#include <stdio.h>
#include <stdlib.h>

struct Card {
    int value;
    int price;
};

int compare(const void *a, const void *b) {
    struct Card *cardA = (struct Card *)a;
    struct Card *cardB = (struct Card *)b;
    return cardA->price - cardB->price;
}

int max(int a, int b) {
    return a > b ? a : b;
}

int abs_val(int x) {
    return x < 0 ? -x : x;
}

int main() {
    int n, k;
    scanf("%d %d", &n, &k);

    struct Card cards[5005];
    for (int i = 0; i < n; i++) {
        scanf("%d %d", &cards[i].value, &cards[i].price);
    }

    if (k == 1) {
        // 如果只选一张卡，直接选价值最大的
        int max_value = 0;
        for (int i = 0; i < n; i++) {
            if (cards[i].value > max_value) {
                max_value = cards[i].value;
            }
        }
        printf("%d\n", max_value);
        return 0;
    }

    // 对于k >= 2的情况，我们需要考虑排列顺序
    // 最优策略是按价格排序，这样相邻卡牌的价格差最小
    qsort(cards, n, sizeof(struct Card), compare);

    // 动态规划：dp[i][j] 表示从前i张卡中选j张的最大收益
    int dp[5005][5005];
    for (int i = 0; i <= n; i++) {
        for (int j = 0; j <= k; j++) {
            dp[i][j] = -1000000000;
        }
    }
    dp[0][0] = 0;

    int min_val(int a, int b) { return a < b ? a : b; }

    for (int i = 1; i <= n; i++) {
        for (int j = 0; j <= min_val(i, k); j++) {
            // 不选第i张卡
            dp[i][j] = dp[i-1][j];

            // 选第i张卡
            if (j > 0) {
                if (j == 1) {
                    // 只选一张卡，没有购买成本
                    dp[i][j] = max(dp[i][j], cards[i-1].value);
                } else {
                    // 选多张卡，需要考虑购买成本
                    // 这里简化处理，假设按价格排序后相邻选择
                    for (int prev = j-1; prev < i; prev++) {
                        if (dp[prev][j-1] > -1000000000) {
                            int cost = 0;
                            if (j > 1) {
                                // 计算与前一张卡的价格差
                                cost = abs_val(cards[i-1].price - cards[prev-1].price);
                            }
                            dp[i][j] = max(dp[i][j], dp[prev][j-1] + cards[i-1].value - cost);
                        }
                    }
                }
            }
        }
    }

    printf("%d\n", dp[n][k]);
    return 0;
}
