#include <stdio.h>
#include <string.h>

int main() {
    int T;
    scanf("%d", &T);

    while (T--) {
        int n, m;
        scanf("%d %d", &n, &m);

        char matrix[1005][1005];
        for (int i = 0; i < n; i++) {
            scanf("%s", matrix[i]);
        }

        // 计算每行和每列的异或和
        int row_xor[1005] = {0};
        int col_xor[1005] = {0};

        for (int i = 0; i < n; i++) {
            for (int j = 0; j < m; j++) {
                int val = matrix[i][j] - '0';
                row_xor[i] ^= val;
                col_xor[j] ^= val;
            }
        }

        // 计算需要改变的位数
        int bad_rows = 0, bad_cols = 0;
        for (int i = 0; i < n; i++) {
            if (row_xor[i] == 1) bad_rows++;
        }
        for (int j = 0; j < m; j++) {
            if (col_xor[j] == 1) bad_cols++;
        }

        int changes;
        if (bad_rows % 2 != bad_cols % 2) {
            // 不可能满足条件
            changes = -1;
        } else if (bad_rows % 2 == 0) {
            // 都是偶数
            changes = (bad_rows + bad_cols) / 2;
        } else {
            // 都是奇数
            changes = (bad_rows + bad_cols) / 2;
        }

        printf("%d\n", changes);
    }

    return 0;
}
