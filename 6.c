#include <stdio.h>
#include <string.h>

struct Card {
    char suit;
    char rank;
    int value;
};

int get_card_value(char rank) {
    if (rank >= '2' && rank <= '9') return rank - '0';
    if (rank == 'X') return 10;
    if (rank == 'J' || rank == 'Q' || rank == 'K') return 10;
    if (rank == 'A') return 11;
    return 0;
}

int get_rank_order(char rank) {
    if (rank >= '2' && rank <= '9') return rank - '0';
    if (rank == 'X') return 10;
    if (rank == 'J') return 11;
    if (rank == 'Q') return 12;
    if (rank == 'K') return 13;
    if (rank == 'A') return 14;
    return 0;
}

int is_straight(int ranks[], int count) {
    if (count != 5) return 0;
    
    // 排序
    for (int i = 0; i < 4; i++) {
        for (int j = i + 1; j < 5; j++) {
            if (ranks[i] > ranks[j]) {
                int temp = ranks[i];
                ranks[i] = ranks[j];
                ranks[j] = temp;
            }
        }
    }
    
    // 检查是否连续
    for (int i = 0; i < 4; i++) {
        if (ranks[i+1] - ranks[i] != 1) return 0;
    }
    return 1;
}

int is_flush(char suits[], int count) {
    if (count != 5) return 0;
    for (int i = 1; i < 5; i++) {
        if (suits[i] != suits[0]) return 0;
    }
    return 1;
}

int evaluate_hand(struct Card cards[], int count) {
    if (count == 0) return 0;
    
    // 统计每种点数的数量
    int rank_count[15] = {0};
    int ranks[5];
    char suits[5];
    int total_value = 0;
    
    for (int i = 0; i < count; i++) {
        int rank_order = get_rank_order(cards[i].rank);
        rank_count[rank_order]++;
        ranks[i] = rank_order;
        suits[i] = cards[i].suit;
        total_value += get_card_value(cards[i].rank);
    }
    
    // 找出各种牌型
    int pairs = 0, three_kind = 0, four_kind = 0;
    for (int i = 2; i <= 14; i++) {
        if (rank_count[i] == 2) pairs++;
        else if (rank_count[i] == 3) three_kind++;
        else if (rank_count[i] == 4) four_kind++;
    }
    
    int base_chips = 0, multiplier = 0;
    int scoring_cards = 0;
    
    if (count == 5) {
        int straight = is_straight(ranks, count);
        int flush = is_flush(suits, count);
        
        if (straight && flush) {
            // 同花顺
            base_chips = 100;
            multiplier = 8;
            scoring_cards = count;
        } else if (four_kind) {
            // 四条
            base_chips = 60;
            multiplier = 7;
            scoring_cards = 4;
        } else if (three_kind && pairs) {
            // 葫芦
            base_chips = 40;
            multiplier = 4;
            scoring_cards = count;
        } else if (flush) {
            // 同花
            base_chips = 35;
            multiplier = 4;
            scoring_cards = count;
        } else if (straight) {
            // 顺子
            base_chips = 30;
            multiplier = 4;
            scoring_cards = count;
        } else if (three_kind) {
            // 三条
            base_chips = 30;
            multiplier = 3;
            scoring_cards = 3;
        } else if (pairs == 2) {
            // 两对
            base_chips = 20;
            multiplier = 2;
            scoring_cards = 4;
        } else if (pairs == 1) {
            // 对子
            base_chips = 10;
            multiplier = 2;
            scoring_cards = 2;
        } else {
            // 高牌
            base_chips = 5;
            multiplier = 1;
            scoring_cards = 1;
        }
    } else if (four_kind) {
        base_chips = 60;
        multiplier = 7;
        scoring_cards = 4;
    } else if (three_kind) {
        base_chips = 30;
        multiplier = 3;
        scoring_cards = 3;
    } else if (pairs == 2) {
        base_chips = 20;
        multiplier = 2;
        scoring_cards = 4;
    } else if (pairs == 1) {
        base_chips = 10;
        multiplier = 2;
        scoring_cards = 2;
    } else {
        base_chips = 5;
        multiplier = 1;
        scoring_cards = 1;
    }
    
    // 计算实际得分的卡牌价值
    int actual_value = 0;
    if (scoring_cards == count) {
        actual_value = total_value;
    } else {
        // 简化处理，取最高价值的卡牌
        actual_value = 0;
        for (int i = 0; i < count && actual_value < scoring_cards * 11; i++) {
            actual_value += get_card_value(cards[i].rank);
        }
    }
    
    return (base_chips + actual_value) * multiplier;
}

int main() {
    int n;
    scanf("%d", &n);
    
    struct Card cards[8];
    for (int i = 0; i < n; i++) {
        scanf(" %c %c", &cards[i].suit, &cards[i].rank);
        cards[i].value = get_card_value(cards[i].rank);
    }
    
    int max_score = 0;
    
    // 尝试所有可能的出牌组合
    for (int mask = 1; mask < (1 << n); mask++) {
        int count = 0;
        struct Card selected[5];
        
        for (int i = 0; i < n; i++) {
            if (mask & (1 << i)) {
                if (count < 5) {
                    selected[count++] = cards[i];
                }
            }
        }
        
        if (count <= 5) {
            int score = evaluate_hand(selected, count);
            if (score > max_score) {
                max_score = score;
            }
        }
    }
    
    printf("%d\n", max_score);
    return 0;
}
