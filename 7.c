#include <stdio.h>
#include <stdlib.h>

struct Constraint {
    int p;
    int x;
};

int compare_constraints(const void *a, const void *b) {
    struct Constraint *ca = (struct Constraint *)a;
    struct Constraint *cb = (struct Constraint *)b;
    return ca->p - cb->p;
}

int main() {
    int T;
    scanf("%d", &T);
    
    while (T--) {
        int n, k;
        scanf("%d %d", &n, &k);
        
        struct Constraint constraints[100005];
        for (int i = 0; i < k; i++) {
            scanf("%d %d", &constraints[i].p, &constraints[i].x);
        }
        
        // 按位置排序
        qsort(constraints, k, sizeof(struct Constraint), compare_constraints);
        
        int result[100005];
        int used[100005] = {0};
        int valid = 1;
        
        // 初始化结果数组
        for (int i = 1; i <= n; i++) {
            result[i] = 0;
        }
        
        // 处理约束条件
        for (int i = 0; i < k; i++) {
            int p = constraints[i].p;
            int x = constraints[i].x;
            
            // 检查约束是否可能满足
            if (x > p || x < 1) {
                valid = 0;
                break;
            }
            
            // 为位置p分配一个值，使得前p个位置中有x个值小于等于它
            // 这意味着result[p]应该是第x小的未使用的数
            int count = 0;
            int value = 1;
            
            // 找到第x个未使用的数
            while (count < x && value <= n) {
                if (!used[value]) {
                    count++;
                    if (count == x) {
                        result[p] = value;
                        used[value] = 1;
                        break;
                    }
                }
                value++;
            }
            
            if (result[p] == 0) {
                valid = 0;
                break;
            }
        }
        
        if (!valid) {
            printf("-1\n");
            continue;
        }
        
        // 填充剩余位置
        int next_value = 1;
        for (int i = 1; i <= n; i++) {
            if (result[i] == 0) {
                while (used[next_value]) {
                    next_value++;
                }
                result[i] = next_value;
                used[next_value] = 1;
            }
        }
        
        // 验证结果
        valid = 1;
        for (int i = 0; i < k; i++) {
            int p = constraints[i].p;
            int x = constraints[i].x;
            int count = 0;
            
            for (int j = 1; j <= p; j++) {
                if (result[j] <= result[p]) {
                    count++;
                }
            }
            
            if (count != x) {
                valid = 0;
                break;
            }
        }
        
        if (!valid) {
            printf("-1\n");
        } else {
            for (int i = 1; i <= n; i++) {
                printf("%d", result[i]);
                if (i < n) printf(" ");
            }
            printf("\n");
        }
    }
    
    return 0;
}
